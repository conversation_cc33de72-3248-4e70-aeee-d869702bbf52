## Operators Support Table

The following table shows ONNX
operators and the supported opset domain/versions in WebGPU EP by ONNX Runtime Web. For example,
`4-6, 8+` means ONNX Runtime Web currently support opset version 4 to 6, 8 and above.

*This file is automatically generated from the
def files via [this script](../script/generate-webgpu-operator-md.ts).
Do not modify directly.*

| Operator | Opset | Comments |
|:--------:|:-------------:|-----|
| Abs | ai.onnx(6-12,13+) |  |
| Acos | ai.onnx(7+) |  |
| Acosh | ai.onnx(9+) |  |
| Add | ai.onnx(7-12,13,14+) |  |
| ArgMax | ai.onnx(1-10,11-12,13+) |  |
| ArgMin | ai.onnx(1-10,11-12,13+) |  |
| Asin | ai.onnx(7+) |  |
| Asinh | ai.onnx(9+) |  |
| Atan | ai.onnx(7+) |  |
| Atanh | ai.onnx(9+) |  |
| Attention | com.microsoft(1+) | need implementing mask and past/present |
| AveragePool | ai.onnx(7-9,10,11-18,19+); com.ms.internal.nhwc(7-9,10,11-18,19+) | need perf optimization; need implementing activation |
| BatchNormalization | ai.onnx(7-8,9-13,14,15+); com.ms.internal.nhwc(7-8,9-13,14,15+) |  |
| BiasAdd | com.microsoft(1+) |  |
| BiasSplitGelu | com.microsoft(1+) |  |
| Cast | ai.onnx(6-8,9-12,13-18,19-20,21+) |  |
| Ceil | ai.onnx(6-12,13+) |  |
| Clip | ai.onnx(6-10,11,12,13+) |  |
| Concat | ai.onnx(1-3,4-10,11-12,13+) |  |
| Conv | ai.onnx(1-10,11+); com.ms.internal.nhwc(1-10,11+) | need perf optimization; conv3d is not supported; need implementing activation |
| ConvTranspose | ai.onnx(1-10,11+); com.ms.internal.nhwc(1-10,11+) | need perf optimization; ConvTranspose3d is not supported; need implementing activation |
| Cos | ai.onnx(7+) |  |
| Cosh | ai.onnx(9+) |  |
| CumSum | ai.onnx(11-13,14+) |  |
| DepthToSpace | ai.onnx(11-12,13+); com.ms.internal.nhwc(11-12,13+) |  |
| DequantizeLinear | ai.onnx(10-12,13-18,19-20,21+) |  |
| Div | ai.onnx(7-12,13,14+) |  |
| Einsum | ai.onnx(12+) |  |
| Elu | ai.onnx(6+) |  |
| Equal | ai.onnx(7-10,11-12,13-18,19+) |  |
| Erf | ai.onnx(9-12,13+) |  |
| Exp | ai.onnx(6-12,13+) |  |
| Expand | ai.onnx(8-12,13+) |  |
| FastGelu | com.microsoft(1+) |  |
| Flatten | ai.onnx(1-8,9-10,11-12,13-20,21+) |  |
| Floor | ai.onnx(6-12,13+) |  |
| FusedConv | com.microsoft(1+) |  |
| Gather | ai.onnx(1-10,11-12,13+) |  |
| GatherBlockQuantized | com.microsoft(1+) |  |
| GatherElements | ai.onnx(11-12,13+) |  |
| GatherND | ai.onnx(11,12,13+) |  |
| Gelu | ai.onnx(20+); com.microsoft(1+) |  |
| Gemm | ai.onnx(7-8,9-10,11-12,13+) |  |
| GlobalAveragePool | ai.onnx(1+); com.ms.internal.nhwc(1+) |  |
| GlobalMaxPool | ai.onnx(1+); com.ms.internal.nhwc(1+) |  |
| Greater | ai.onnx(7-8,9-12,13+) |  |
| GreaterOrEqual | ai.onnx(12-15,16+) |  |
| GridSample | ai.onnx(16-19); com.ms.internal.nhwc(16-19) |  |
| GroupQueryAttention | com.microsoft(1+) |  |
| HardSigmoid | ai.onnx(6+) |  |
| If | ai.onnx(1-10,11-12,13-18,19-20,21+) |  |
| InstanceNormalization | ai.onnx(6+); com.ms.internal.nhwc(6+) |  |
| LayerNormalization | ai.onnx(1-16,17+) |  |
| LeakyRelu | ai.onnx(6-15,16+) |  |
| Less | ai.onnx(7-8,9-12,13+) |  |
| LessOrEqual | ai.onnx(12-15,16+) |  |
| Log | ai.onnx(6-12,13+) |  |
| MatMul | ai.onnx(1-12,13+) |  |
| MatMulNBits | com.microsoft(1+) |  |
| MaxPool | ai.onnx(1-7,8-9,10,11,12+); com.ms.internal.nhwc(1-7,8-9,10,11,12+) | need perf optimization; need implementing activation |
| MemcpyFromHost | ai.onnx(1+) |  |
| MemcpyToHost | ai.onnx(1+) |  |
| Mul | ai.onnx(7-12,13,14+) |  |
| MultiHeadAttention | com.microsoft(1+) | need implementing mask and past/present |
| Neg | ai.onnx(6-12,13+) |  |
| Not | ai.onnx(1+) |  |
| Pad | ai.onnx(2-10,11-12,13-17,18,19-20,21+) |  |
| Pow | ai.onnx(7-11,12,13-14,15+) |  |
| QuickGelu | com.microsoft(1+) |  |
| Range | ai.onnx(11+) |  |
| Reciprocal | ai.onnx(6-12,13+) |  |
| ReduceL1 | ai.onnx(1-10,11-12,13-17,18+) |  |
| ReduceL2 | ai.onnx(1-10,11-12,13-17,18+) |  |
| ReduceLogSum | ai.onnx(1-10,11-12,13-17,18+) |  |
| ReduceLogSumExp | ai.onnx(1-10,11-12,13-17,18+) |  |
| ReduceMax | ai.onnx(1-10,11,12,13-17,18-19,20+) |  |
| ReduceMean | ai.onnx(1-10,11-12,13-17,18+) |  |
| ReduceMin | ai.onnx(1-10,11,12,13-17,18-19,20+) |  |
| ReduceProd | ai.onnx(1-10,11-12,13-17,18+) |  |
| ReduceSum | ai.onnx(1-10,11-12,13+) |  |
| ReduceSumSquare | ai.onnx(1-10,11-12,13-17,18+) |  |
| Relu | ai.onnx(6-12,13,14+) |  |
| Reshape | ai.onnx(5-12,13,14-18,19-20,21+) | no GPU kernel |
| Resize | ai.onnx(10,11-12,13-17,18,19+); com.ms.internal.nhwc(10,11-12,13-17,18,19+) | CoordinateTransformMode align_corners is not supported with downsampling |
| RotaryEmbedding | com.microsoft(1+) |  |
| ScatterND | ai.onnx(11-12,13-15,16-17,18+) |  |
| Shape | ai.onnx(1-12,13-14,15-18,19-20,21+) | no GPU kernel; an ORT warning is generated - need to fix |
| Sigmoid | ai.onnx(6-12,13+) |  |
| SimplifiedLayerNormalization | ai.onnx(1+) |  |
| Sin | ai.onnx(7+) |  |
| Sinh | ai.onnx(9+) |  |
| SkipLayerNormalization | com.microsoft(1+) |  |
| SkipSimplifiedLayerNormalization | com.microsoft(1+) |  |
| Slice | ai.onnx(1-9,10,11-12,13+) |  |
| Softmax | ai.onnx(1-10,11-12,13+) |  |
| Split | ai.onnx(1,2-10,11-12,13-17,18+) |  |
| Sqrt | ai.onnx(6-12,13+) |  |
| Squeeze | ai.onnx(1-10,11-12,13-20,21+) |  |
| Sub | ai.onnx(7-12,13,14+) |  |
| Tan | ai.onnx(7+) |  |
| Tanh | ai.onnx(6-12,13+) |  |
| ThresholdedRelu | ai.onnx(10+) |  |
| Tile | ai.onnx(6-12,13+) |  |
| Transpose | ai.onnx(1-12,13-20,21+) | need perf optimization |
| Unsqueeze | ai.onnx(1-10,11-12,13-20,21+) |  |
| Where | ai.onnx(9-15,16+) |  |
