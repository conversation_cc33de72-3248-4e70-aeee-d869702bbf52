(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@ricky0123+vad-web@0.0.24/node_modules/@ricky0123/vad-web/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_d4d59429._.js",
  "static/chunks/e2441_@ricky0123_vad-web_dist_index_3f2e0014.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@ricky0123+vad-web@0.0.24/node_modules/@ricky0123/vad-web/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);