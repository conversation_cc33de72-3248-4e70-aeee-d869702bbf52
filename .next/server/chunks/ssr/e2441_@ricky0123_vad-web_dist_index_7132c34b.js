module.exports = {

"[project]/node_modules/.pnpm/@ricky0123+vad-web@0.0.24/node_modules/@ricky0123/vad-web/dist/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_67a170cc._.js",
  "server/chunks/ssr/[root-of-the-server]__db8bcc0c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@ricky0123+vad-web@0.0.24/node_modules/@ricky0123/vad-web/dist/index.js [app-ssr] (ecmascript)");
    });
});
}}),

};