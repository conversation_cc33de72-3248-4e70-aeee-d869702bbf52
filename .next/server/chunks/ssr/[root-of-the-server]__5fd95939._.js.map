{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/components/VideoCall.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface VideoCallProps {\n  isActive: boolean;\n  isListening: boolean;\n  isAISpeaking: boolean;\n  currentQuestion: string;\n  onStartCall: () => void;\n  onEndCall: () => void;\n}\n\nexport default function VideoCall({\n  isActive,\n  isListening,\n  isAISpeaking,\n  currentQuestion,\n  onStartCall,\n  onEndCall\n}: VideoCallProps) {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const [stream, setStream] = useState<MediaStream | null>(null);\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    initializeCamera();\n    return () => {\n      if (stream) {\n        stream.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, []);\n\n  const initializeCamera = async () => {\n    try {\n      const mediaStream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: { ideal: 1280 },\n          height: { ideal: 720 },\n          facingMode: 'user'\n        },\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        }\n      });\n      \n      setStream(mediaStream);\n      setHasPermission(true);\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = mediaStream;\n      }\n    } catch (error) {\n      console.error('Error accessing camera:', error);\n      setHasPermission(false);\n    }\n  };\n\n  if (hasPermission === false) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center text-white\">\n          <div className=\"text-6xl mb-4\">📹</div>\n          <h2 className=\"text-2xl font-bold mb-4\">Camera Access Required</h2>\n          <p className=\"text-gray-400 mb-6\">\n            Please allow camera and microphone access to start your pitch practice.\n          </p>\n          <button\n            onClick={initializeCamera}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg\"\n          >\n            Grant Access\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black flex flex-col\">\n      {/* Main video area */}\n      <div className=\"flex-1 flex\">\n        {/* User video */}\n        <div className=\"flex-1 relative\">\n          <video\n            ref={videoRef}\n            autoPlay\n            muted\n            playsInline\n            className=\"w-full h-full object-cover\"\n          />\n          \n          {/* User label */}\n          <div className=\"absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm\">\n            You\n          </div>\n          \n          {/* Listening indicator */}\n          <AnimatePresence>\n            {isListening && (\n              <motion.div\n                initial={{ scale: 0.8, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.8, opacity: 0 }}\n                className=\"absolute top-4 left-4 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2\"\n              >\n                <motion.div\n                  animate={{ scale: [1, 1.2, 1] }}\n                  transition={{ repeat: Infinity, duration: 1 }}\n                  className=\"w-2 h-2 bg-white rounded-full\"\n                />\n                <span>Listening...</span>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* AI/VC video area */}\n        <div className=\"flex-1 relative bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center\">\n          <div className=\"text-center text-white\">\n            {/* AI Avatar */}\n            <motion.div\n              animate={isAISpeaking ? { scale: [1, 1.05, 1] } : {}}\n              transition={{ repeat: isAISpeaking ? Infinity : 0, duration: 2 }}\n              className=\"w-40 h-40 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-2xl\"\n            >\n              <span className=\"text-6xl font-bold\">K</span>\n            </motion.div>\n            \n            <h3 className=\"text-2xl font-semibold mb-2\">Kenard (CEO)</h3>\n            <p className=\"text-gray-400 text-sm mb-4\">Venture Capital Partner</p>\n            \n            {/* Speaking indicator */}\n            <AnimatePresence>\n              {isAISpeaking && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  className=\"flex items-center justify-center space-x-1\"\n                >\n                  <div className=\"text-green-400 text-sm font-medium\">Speaking</div>\n                  <motion.div\n                    animate={{ scale: [1, 1.2, 1] }}\n                    transition={{ repeat: Infinity, duration: 0.8 }}\n                    className=\"w-2 h-2 bg-green-400 rounded-full\"\n                  />\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n          \n          {/* VC label */}\n          <div className=\"absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm\">\n            Kenard (CEO)\n          </div>\n        </div>\n      </div>\n\n      {/* Question display */}\n      <AnimatePresence>\n        {currentQuestion && isActive && (\n          <motion.div\n            initial={{ y: 100, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            exit={{ y: 100, opacity: 0 }}\n            className=\"bg-gray-900/95 backdrop-blur-sm p-6 border-t border-gray-700\"\n          >\n            <div className=\"max-w-4xl mx-auto text-center\">\n              <p className=\"text-white text-lg font-medium\">{currentQuestion}</p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Start call overlay */}\n      <AnimatePresence>\n        {!isActive && hasPermission && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center\"\n          >\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"mb-8\"\n              >\n                <h2 className=\"text-3xl font-bold text-white mb-4\">\n                  Ready to Start Your Pitch?\n                </h2>\n                <p className=\"text-gray-300 text-lg\">\n                  You'll be interviewed by Kenard, an experienced VC partner\n                </p>\n              </motion.div>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={onStartCall}\n                className=\"bg-green-600 hover:bg-green-700 text-white text-xl font-semibold px-12 py-4 rounded-xl shadow-lg transition-colors\"\n              >\n                Start Call\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAce,SAAS,UAAU,EAChC,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,eAAe,EACf,WAAW,EACX,SAAS,EACM;IACf,MAAM,WAAW,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAsB;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,OAAO;YACL,IAAI,QAAQ;gBACV,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAChD;QACF;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,cAAc,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAC5D,OAAO;oBACL,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAI;oBACrB,YAAY;gBACd;gBACA,OAAO;oBACL,kBAAkB;oBAClB,kBAAkB;oBAClB,iBAAiB;gBACnB;YACF;YAEA,UAAU;YACV,iBAAiB;YAEjB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,iBAAiB;QACnB;IACF;IAEA,IAAI,kBAAkB,OAAO;QAC3B,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6WAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6WAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6WAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCACC,KAAK;gCACL,QAAQ;gCACR,KAAK;gCACL,WAAW;gCACX,WAAU;;;;;;0CAIZ,6WAAC;gCAAI,WAAU;0CAAwF;;;;;;0CAKvG,6WAAC,qUAAA,CAAA,kBAAe;0CACb,6BACC,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;wCAAK,SAAS;oCAAE;oCAClC,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,MAAM;wCAAE,OAAO;wCAAK,SAAS;oCAAE;oCAC/B,WAAU;;sDAEV,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,QAAQ;gDAAU,UAAU;4CAAE;4CAC5C,WAAU;;;;;;sDAEZ,6WAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDAEb,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS,eAAe;4CAAE,OAAO;gDAAC;gDAAG;gDAAM;6CAAE;wCAAC,IAAI,CAAC;wCACnD,YAAY;4CAAE,QAAQ,eAAe,WAAW;4CAAG,UAAU;wCAAE;wCAC/D,WAAU;kDAEV,cAAA,6WAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;kDAGvC,6WAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6WAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6WAAC,qUAAA,CAAA,kBAAe;kDACb,8BACC,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC1B,WAAU;;8DAEV,6WAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;4DAAC;4DAAG;4DAAK;yDAAE;oDAAC;oDAC9B,YAAY;wDAAE,QAAQ;wDAAU,UAAU;oDAAI;oDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,6WAAC;gCAAI,WAAU;0CAAyF;;;;;;;;;;;;;;;;;;0BAO5G,6WAAC,qUAAA,CAAA,kBAAe;0BACb,mBAAmB,0BAClB,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;wBAAK,SAAS;oBAAE;oBAC9B,SAAS;wBAAE,GAAG;wBAAG,SAAS;oBAAE;oBAC5B,MAAM;wBAAE,GAAG;wBAAK,SAAS;oBAAE;oBAC3B,WAAU;8BAEV,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;;;;;0BAOvD,6WAAC,qUAAA,CAAA,kBAAe;0BACb,CAAC,YAAY,+BACZ,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;8BAEV,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;;kDAEV,6WAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,6WAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6WAAC,sUAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/components/AudioHandler.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState, useCallback } from 'react';\n\ninterface AudioHandlerProps {\n  isListening: boolean;\n  onSpeechResult: (transcript: string) => void;\n  onSpeechEnd: () => void;\n  onError: (error: string) => void;\n}\n\ndeclare global {\n  interface Window {\n    SpeechRecognition: any;\n    webkitSpeechRecognition: any;\n  }\n}\n\nexport default function AudioHandler({\n  isListening,\n  onSpeechResult,\n  onSpeechEnd,\n  onError\n}: AudioHandlerProps) {\n  const recognitionRef = useRef<any>(null);\n  const vadRef = useRef<any>(null);\n  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const isRestartingRef = useRef(false);\n  const lastSpeechTimeRef = useRef<number>(0);\n  const speechBufferRef = useRef<string>('');\n\n  const [isSupported, setIsSupported] = useState(false);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [isVADActive, setIsVADActive] = useState(false);\n  const [vadSupported, setVadSupported] = useState(false);\n\n  // Initialize Voice Activity Detection\n  const initializeVAD = useCallback(async () => {\n    try {\n      // Dynamically import VAD to avoid SSR issues\n      const { MicVAD } = await import('@ricky0123/vad-web');\n\n      const vad = await MicVAD.new({\n        onSpeechStart: () => {\n          console.log('VAD: Speech detected');\n          setIsVADActive(true);\n          lastSpeechTimeRef.current = Date.now();\n\n          // If speech recognition isn't running, start it\n          if (isListening && recognitionRef.current && !isRestartingRef.current) {\n            try {\n              recognitionRef.current.start();\n            } catch (error) {\n              console.log('Recognition already running or starting');\n            }\n          }\n        },\n        onSpeechEnd: (_audio: Float32Array) => {\n          console.log('VAD: Speech ended');\n          setIsVADActive(false);\n\n          // Don't immediately stop recognition, wait for a brief pause\n          // This helps with natural conversation flow\n          setTimeout(() => {\n            if (Date.now() - lastSpeechTimeRef.current > 1500) {\n              setIsVADActive(false);\n            }\n          }, 1500);\n        },\n        onVADMisfire: () => {\n          console.log('VAD: Misfire detected');\n        },\n        // Enhanced VAD settings for better detection\n        positiveSpeechThreshold: 0.6, // Lower threshold for more sensitive detection\n        negativeSpeechThreshold: 0.35,\n        redemptionFrames: 8, // More frames to confirm speech end\n        frameSamples: 1536, // Optimal frame size\n        preSpeechPadFrames: 1, // Capture a bit before speech starts\n        minSpeechFrames: 4, // Minimum frames to consider as speech\n        submitUserSpeechOnPause: true\n      });\n\n      vadRef.current = vad;\n      setVadSupported(true);\n      console.log('VAD initialized successfully');\n    } catch (error) {\n      console.warn('VAD initialization failed:', error);\n      setVadSupported(false);\n    }\n  }, [isListening]);\n\n  // Enhanced speech recognition initialization\n  const initializeSpeechRecognition = useCallback(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n\n    if (!SpeechRecognition) {\n      setIsSupported(false);\n      onError('Speech recognition is not supported in this browser');\n      return;\n    }\n\n    setIsSupported(true);\n\n    const recognition = new SpeechRecognition();\n\n    // Enhanced recognition settings\n    recognition.continuous = true;\n    recognition.interimResults = true;\n    recognition.lang = 'en-US';\n    recognition.maxAlternatives = 3; // Get multiple alternatives for better accuracy\n    recognition.serviceURI = ''; // Use default service\n\n    // Enhanced event handlers\n    recognition.onstart = () => {\n      console.log('Speech recognition started');\n      isRestartingRef.current = false;\n\n      // Clear any pending restart\n      if (restartTimeoutRef.current) {\n        clearTimeout(restartTimeoutRef.current);\n        restartTimeoutRef.current = null;\n      }\n    };\n\n    recognition.onresult = (event: any) => {\n      let finalTranscript = '';\n      let interimTranscript = '';\n      let confidence = 0;\n\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const transcript = result[0].transcript;\n\n        if (result.isFinal) {\n          finalTranscript += transcript;\n          confidence = result[0].confidence || 0;\n\n          // Update last speech time\n          lastSpeechTimeRef.current = Date.now();\n        } else {\n          interimTranscript += transcript;\n        }\n      }\n\n      const fullTranscript = finalTranscript || interimTranscript;\n      setCurrentTranscript(fullTranscript);\n\n      // Only process final results with reasonable confidence\n      if (finalTranscript && (confidence > 0.3 || confidence === 0)) {\n        const trimmedTranscript = finalTranscript.trim();\n\n        if (trimmedTranscript.length > 0) {\n          // Add to speech buffer to handle overlapping speech\n          speechBufferRef.current += ' ' + trimmedTranscript;\n\n          // Process accumulated speech after a brief delay\n          setTimeout(() => {\n            if (speechBufferRef.current.trim()) {\n              onSpeechResult(speechBufferRef.current.trim());\n              speechBufferRef.current = '';\n            }\n          }, 500);\n        }\n      }\n    };\n\n    recognition.onerror = (event: any) => {\n      console.error('Speech recognition error:', event.error);\n\n      // Handle different error types\n      if (event.error === 'network') {\n        console.log('Network error, will retry...');\n        scheduleRestart();\n      } else if (event.error === 'not-allowed') {\n        onError('Microphone access denied. Please allow microphone access and refresh the page.');\n      } else if (event.error === 'no-speech') {\n        console.log('No speech detected, restarting...');\n        scheduleRestart();\n      } else {\n        onError(`Speech recognition error: ${event.error}`);\n        scheduleRestart();\n      }\n    };\n\n    recognition.onend = () => {\n      console.log('Speech recognition ended');\n\n      // Auto-restart if we're supposed to be listening\n      if (isListening && !isRestartingRef.current) {\n        console.log('Auto-restarting speech recognition...');\n        scheduleRestart();\n      } else {\n        setCurrentTranscript('');\n        if (!isListening) {\n          onSpeechEnd();\n        }\n      }\n    };\n\n    recognitionRef.current = recognition;\n  }, [onSpeechResult, onSpeechEnd, onError, isListening]);\n\n  // Smart restart mechanism\n  const scheduleRestart = useCallback(() => {\n    if (isRestartingRef.current || !isListening) return;\n\n    isRestartingRef.current = true;\n\n    // Clear any existing restart timeout\n    if (restartTimeoutRef.current) {\n      clearTimeout(restartTimeoutRef.current);\n    }\n\n    // Restart after a brief delay to avoid rapid restarts\n    restartTimeoutRef.current = setTimeout(() => {\n      if (isListening && recognitionRef.current) {\n        try {\n          console.log('Restarting speech recognition...');\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error('Error restarting recognition:', error);\n          isRestartingRef.current = false;\n\n          // Try again after a longer delay\n          setTimeout(() => {\n            if (isListening) {\n              scheduleRestart();\n            }\n          }, 2000);\n        }\n      } else {\n        isRestartingRef.current = false;\n      }\n    }, 100);\n  }, [isListening]);\n\n  // Initialize everything\n  useEffect(() => {\n    initializeSpeechRecognition();\n    initializeVAD();\n\n    return () => {\n      // Cleanup\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n      if (vadRef.current) {\n        vadRef.current.destroy?.();\n      }\n      if (restartTimeoutRef.current) {\n        clearTimeout(restartTimeoutRef.current);\n      }\n    };\n  }, [initializeSpeechRecognition, initializeVAD]);\n\n  // Control speech recognition based on listening state\n  useEffect(() => {\n    if (!isSupported || !recognitionRef.current) return;\n\n    if (isListening) {\n      try {\n        // Start VAD if available\n        if (vadRef.current) {\n          vadRef.current.start();\n        }\n\n        // Start speech recognition\n        recognitionRef.current.start();\n      } catch (error) {\n        console.error('Error starting speech recognition:', error);\n      }\n    } else {\n      try {\n        // Stop VAD if available\n        if (vadRef.current) {\n          vadRef.current.pause();\n        }\n\n        // Stop speech recognition\n        recognitionRef.current.stop();\n\n        // Clear any pending restarts\n        if (restartTimeoutRef.current) {\n          clearTimeout(restartTimeoutRef.current);\n          restartTimeoutRef.current = null;\n        }\n\n        isRestartingRef.current = false;\n        setCurrentTranscript('');\n      } catch (error) {\n        console.error('Error stopping speech recognition:', error);\n      }\n    }\n  }, [isListening, isSupported]);\n\n  const speakText = useCallback(async (text: string): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      if (!('speechSynthesis' in window)) {\n        reject(new Error('Speech synthesis not supported'));\n        return;\n      }\n\n      // Cancel any ongoing speech\n      window.speechSynthesis.cancel();\n\n      const utterance = new SpeechSynthesisUtterance(text);\n      \n      // Configure voice settings\n      utterance.rate = 0.9;\n      utterance.pitch = 1.0;\n      utterance.volume = 1.0;\n\n      // Try to use a professional-sounding voice\n      const voices = window.speechSynthesis.getVoices();\n      const preferredVoice = voices.find(voice => \n        voice.name.includes('Alex') || \n        voice.name.includes('Daniel') || \n        voice.name.includes('Google US English')\n      );\n      \n      if (preferredVoice) {\n        utterance.voice = preferredVoice;\n      }\n\n      utterance.onend = () => {\n        resolve();\n      };\n\n      utterance.onerror = (event) => {\n        reject(new Error(`Speech synthesis error: ${event.error}`));\n      };\n\n      window.speechSynthesis.speak(utterance);\n    });\n  }, []);\n\n  // Expose speakText function to parent components\n  useEffect(() => {\n    (window as any).pitchpalSpeakText = speakText;\n    return () => {\n      delete (window as any).pitchpalSpeakText;\n    };\n  }, [speakText]);\n\n  if (!isSupported) {\n    return (\n      <div className=\"fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg\">\n        <p className=\"text-sm\">\n          Speech recognition is not supported in this browser.\n          Please use Chrome, Edge, or Safari for the best experience.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* Enhanced live transcript display with VAD indicator */}\n      {currentTranscript && isListening && (\n        <div className=\"fixed bottom-20 left-4 right-4 bg-black/90 text-white p-4 rounded-lg backdrop-blur-sm border border-gray-700\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"text-sm text-gray-400\">You're saying:</div>\n            <div className=\"flex items-center space-x-2\">\n              {vadSupported && (\n                <div className={`flex items-center space-x-1 text-xs ${isVADActive ? 'text-green-400' : 'text-gray-500'}`}>\n                  <div className={`w-2 h-2 rounded-full ${isVADActive ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`} />\n                  <span>VAD</span>\n                </div>\n              )}\n              <div className=\"flex items-center space-x-1 text-xs text-blue-400\">\n                <div className=\"w-2 h-2 rounded-full bg-blue-400 animate-pulse\" />\n                <span>Listening</span>\n              </div>\n            </div>\n          </div>\n          <div className=\"text-white font-medium\">{currentTranscript}</div>\n          {vadSupported && (\n            <div className=\"text-xs text-gray-500 mt-2\">\n              Enhanced with Voice Activity Detection for better pickup\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Audio quality indicator */}\n      {isListening && (\n        <div className=\"fixed top-4 right-4 bg-black/80 text-white p-3 rounded-lg backdrop-blur-sm border border-gray-700\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-green-400 animate-pulse\" />\n            <div className=\"text-sm\">\n              <div className=\"font-medium\">Audio Active</div>\n              <div className=\"text-xs text-gray-400\">\n                {vadSupported ? 'VAD + Speech Recognition' : 'Speech Recognition Only'}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n\n// Utility function to synthesize speech using ElevenLabs API\nexport async function synthesizeWithElevenLabs(text: string): Promise<void> {\n  try {\n    const response = await fetch('/api/voice', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ text }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`Voice synthesis failed: ${errorData.error || response.statusText}`);\n    }\n\n    // Check if response is audio data\n    const contentType = response.headers.get('content-type');\n    if (contentType && contentType.includes('audio')) {\n      // Response is audio data, play it directly\n      const audioBlob = await response.blob();\n      const audioUrl = URL.createObjectURL(audioBlob);\n      await playAudio(audioUrl);\n      URL.revokeObjectURL(audioUrl);\n    } else {\n      // Fallback to browser speech synthesis\n      throw new Error('No audio data received from ElevenLabs');\n    }\n  } catch (error) {\n    console.error('ElevenLabs synthesis error:', error);\n    // Fallback to browser speech synthesis\n    if ('speechSynthesis' in window) {\n      return new Promise((resolve, reject) => {\n        const utterance = new SpeechSynthesisUtterance(text);\n        utterance.onend = () => resolve();\n        utterance.onerror = () => reject(new Error('Browser synthesis failed'));\n        window.speechSynthesis.speak(utterance);\n      });\n    }\n    throw error;\n  }\n}\n\n// Utility function to play audio from URL\nexport function playAudio(audioUrl: string): Promise<void> {\n  return new Promise((resolve, reject) => {\n    const audio = new Audio(audioUrl);\n    \n    audio.onended = () => resolve();\n    audio.onerror = () => reject(new Error('Audio playback failed'));\n    \n    audio.play().catch(reject);\n  });\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAkBe,SAAS,aAAa,EACnC,WAAW,EACX,cAAc,EACd,WAAW,EACX,OAAO,EACW;IAClB,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAO;IACnC,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAO;IAC3B,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAyB;IACxD,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAU;IACzC,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAU;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,sCAAsC;IACtC,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,6CAA6C;YAC7C,MAAM,EAAE,MAAM,EAAE,GAAG;YAEnB,MAAM,MAAM,MAAM,OAAO,GAAG,CAAC;gBAC3B,eAAe;oBACb,QAAQ,GAAG,CAAC;oBACZ,eAAe;oBACf,kBAAkB,OAAO,GAAG,KAAK,GAAG;oBAEpC,gDAAgD;oBAChD,IAAI,eAAe,eAAe,OAAO,IAAI,CAAC,gBAAgB,OAAO,EAAE;wBACrE,IAAI;4BACF,eAAe,OAAO,CAAC,KAAK;wBAC9B,EAAE,OAAO,OAAO;4BACd,QAAQ,GAAG,CAAC;wBACd;oBACF;gBACF;gBACA,aAAa,CAAC;oBACZ,QAAQ,GAAG,CAAC;oBACZ,eAAe;oBAEf,6DAA6D;oBAC7D,4CAA4C;oBAC5C,WAAW;wBACT,IAAI,KAAK,GAAG,KAAK,kBAAkB,OAAO,GAAG,MAAM;4BACjD,eAAe;wBACjB;oBACF,GAAG;gBACL;gBACA,cAAc;oBACZ,QAAQ,GAAG,CAAC;gBACd;gBACA,6CAA6C;gBAC7C,yBAAyB;gBACzB,yBAAyB;gBACzB,kBAAkB;gBAClB,cAAc;gBACd,oBAAoB;gBACpB,iBAAiB;gBACjB,yBAAyB;YAC3B;YAEA,OAAO,OAAO,GAAG;YACjB,gBAAgB;YAChB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,8BAA8B;YAC3C,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAY;IAEhB,6CAA6C;IAC7C,MAAM,8BAA8B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC9C,MAAM,oBAAoB,OAAO,iBAAiB,IAAI,OAAO,uBAAuB;QAEpF,IAAI,CAAC,mBAAmB;YACtB,eAAe;YACf,QAAQ;YACR;QACF;QAEA,eAAe;QAEf,MAAM,cAAc,IAAI;QAExB,gCAAgC;QAChC,YAAY,UAAU,GAAG;QACzB,YAAY,cAAc,GAAG;QAC7B,YAAY,IAAI,GAAG;QACnB,YAAY,eAAe,GAAG,GAAG,gDAAgD;QACjF,YAAY,UAAU,GAAG,IAAI,sBAAsB;QAEnD,0BAA0B;QAC1B,YAAY,OAAO,GAAG;YACpB,QAAQ,GAAG,CAAC;YACZ,gBAAgB,OAAO,GAAG;YAE1B,4BAA4B;YAC5B,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,aAAa,kBAAkB,OAAO;gBACtC,kBAAkB,OAAO,GAAG;YAC9B;QACF;QAEA,YAAY,QAAQ,GAAG,CAAC;YACtB,IAAI,kBAAkB;YACtB,IAAI,oBAAoB;YACxB,IAAI,aAAa;YAEjB,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;gBAC7D,MAAM,SAAS,MAAM,OAAO,CAAC,EAAE;gBAC/B,MAAM,aAAa,MAAM,CAAC,EAAE,CAAC,UAAU;gBAEvC,IAAI,OAAO,OAAO,EAAE;oBAClB,mBAAmB;oBACnB,aAAa,MAAM,CAAC,EAAE,CAAC,UAAU,IAAI;oBAErC,0BAA0B;oBAC1B,kBAAkB,OAAO,GAAG,KAAK,GAAG;gBACtC,OAAO;oBACL,qBAAqB;gBACvB;YACF;YAEA,MAAM,iBAAiB,mBAAmB;YAC1C,qBAAqB;YAErB,wDAAwD;YACxD,IAAI,mBAAmB,CAAC,aAAa,OAAO,eAAe,CAAC,GAAG;gBAC7D,MAAM,oBAAoB,gBAAgB,IAAI;gBAE9C,IAAI,kBAAkB,MAAM,GAAG,GAAG;oBAChC,oDAAoD;oBACpD,gBAAgB,OAAO,IAAI,MAAM;oBAEjC,iDAAiD;oBACjD,WAAW;wBACT,IAAI,gBAAgB,OAAO,CAAC,IAAI,IAAI;4BAClC,eAAe,gBAAgB,OAAO,CAAC,IAAI;4BAC3C,gBAAgB,OAAO,GAAG;wBAC5B;oBACF,GAAG;gBACL;YACF;QACF;QAEA,YAAY,OAAO,GAAG,CAAC;YACrB,QAAQ,KAAK,CAAC,6BAA6B,MAAM,KAAK;YAEtD,+BAA+B;YAC/B,IAAI,MAAM,KAAK,KAAK,WAAW;gBAC7B,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO,IAAI,MAAM,KAAK,KAAK,eAAe;gBACxC,QAAQ;YACV,OAAO,IAAI,MAAM,KAAK,KAAK,aAAa;gBACtC,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,QAAQ,CAAC,0BAA0B,EAAE,MAAM,KAAK,EAAE;gBAClD;YACF;QACF;QAEA,YAAY,KAAK,GAAG;YAClB,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,IAAI,eAAe,CAAC,gBAAgB,OAAO,EAAE;gBAC3C,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,qBAAqB;gBACrB,IAAI,CAAC,aAAa;oBAChB;gBACF;YACF;QACF;QAEA,eAAe,OAAO,GAAG;IAC3B,GAAG;QAAC;QAAgB;QAAa;QAAS;KAAY;IAEtD,0BAA0B;IAC1B,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,gBAAgB,OAAO,IAAI,CAAC,aAAa;QAE7C,gBAAgB,OAAO,GAAG;QAE1B,qCAAqC;QACrC,IAAI,kBAAkB,OAAO,EAAE;YAC7B,aAAa,kBAAkB,OAAO;QACxC;QAEA,sDAAsD;QACtD,kBAAkB,OAAO,GAAG,WAAW;YACrC,IAAI,eAAe,eAAe,OAAO,EAAE;gBACzC,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,eAAe,OAAO,CAAC,KAAK;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,gBAAgB,OAAO,GAAG;oBAE1B,iCAAiC;oBACjC,WAAW;wBACT,IAAI,aAAa;4BACf;wBACF;oBACF,GAAG;gBACL;YACF,OAAO;gBACL,gBAAgB,OAAO,GAAG;YAC5B;QACF,GAAG;IACL,GAAG;QAAC;KAAY;IAEhB,wBAAwB;IACxB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QAEA,OAAO;YACL,UAAU;YACV,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,IAAI;YAC7B;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,CAAC,OAAO;YACxB;YACA,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,aAAa,kBAAkB,OAAO;YACxC;QACF;IACF,GAAG;QAAC;QAA6B;KAAc;IAE/C,sDAAsD;IACtD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,eAAe,OAAO,EAAE;QAE7C,IAAI,aAAa;YACf,IAAI;gBACF,yBAAyB;gBACzB,IAAI,OAAO,OAAO,EAAE;oBAClB,OAAO,OAAO,CAAC,KAAK;gBACtB;gBAEA,2BAA2B;gBAC3B,eAAe,OAAO,CAAC,KAAK;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF,OAAO;YACL,IAAI;gBACF,wBAAwB;gBACxB,IAAI,OAAO,OAAO,EAAE;oBAClB,OAAO,OAAO,CAAC,KAAK;gBACtB;gBAEA,0BAA0B;gBAC1B,eAAe,OAAO,CAAC,IAAI;gBAE3B,6BAA6B;gBAC7B,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,aAAa,kBAAkB,OAAO;oBACtC,kBAAkB,OAAO,GAAG;gBAC9B;gBAEA,gBAAgB,OAAO,GAAG;gBAC1B,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;IACF,GAAG;QAAC;QAAa;KAAY;IAE7B,MAAM,YAAY,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,CAAC,qBAAqB,MAAM,GAAG;gBAClC,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,4BAA4B;YAC5B,OAAO,eAAe,CAAC,MAAM;YAE7B,MAAM,YAAY,IAAI,yBAAyB;YAE/C,2BAA2B;YAC3B,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,2CAA2C;YAC3C,MAAM,SAAS,OAAO,eAAe,CAAC,SAAS;YAC/C,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,WACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,aACpB,MAAM,IAAI,CAAC,QAAQ,CAAC;YAGtB,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,KAAK,GAAG;gBAChB;YACF;YAEA,UAAU,OAAO,GAAG,CAAC;gBACnB,OAAO,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,KAAK,EAAE;YAC3D;YAEA,OAAO,eAAe,CAAC,KAAK,CAAC;QAC/B;IACF,GAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACP,OAAe,iBAAiB,GAAG;QACpC,OAAO;YACL,OAAO,AAAC,OAAe,iBAAiB;QAC1C;IACF,GAAG;QAAC;KAAU;IAEd,IAAI,CAAC,aAAa;QAChB,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAE,WAAU;0BAAU;;;;;;;;;;;IAM7B;IAEA,qBACE;;YAEG,qBAAqB,6BACpB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CAAwB;;;;;;0CACvC,6WAAC;gCAAI,WAAU;;oCACZ,8BACC,6WAAC;wCAAI,WAAW,CAAC,oCAAoC,EAAE,cAAc,mBAAmB,iBAAiB;;0DACvG,6WAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,cAAc,+BAA+B,eAAe;;;;;;0DACpG,6WAAC;0DAAK;;;;;;;;;;;;kDAGV,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;;;;;0DACf,6WAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAIZ,6WAAC;wBAAI,WAAU;kCAA0B;;;;;;oBACxC,8BACC,6WAAC;wBAAI,WAAU;kCAA6B;;;;;;;;;;;;YAQjD,6BACC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CAAc;;;;;;8CAC7B,6WAAC;oCAAI,WAAU;8CACZ,eAAe,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;AAGO,eAAe,yBAAyB,IAAY;IACzD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,cAAc;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAK;QAC9B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,UAAU,KAAK,IAAI,SAAS,UAAU,EAAE;QACrF;QAEA,kCAAkC;QAClC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,UAAU;YAChD,2CAA2C;YAC3C,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,WAAW,IAAI,eAAe,CAAC;YACrC,MAAM,UAAU;YAChB,IAAI,eAAe,CAAC;QACtB,OAAO;YACL,uCAAuC;YACvC,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,uCAAuC;QACvC,IAAI,qBAAqB,QAAQ;YAC/B,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,MAAM,YAAY,IAAI,yBAAyB;gBAC/C,UAAU,KAAK,GAAG,IAAM;gBACxB,UAAU,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;gBAC3C,OAAO,eAAe,CAAC,KAAK,CAAC;YAC/B;QACF;QACA,MAAM;IACR;AACF;AAGO,SAAS,UAAU,QAAgB;IACxC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,QAAQ,IAAI,MAAM;QAExB,MAAM,OAAO,GAAG,IAAM;QACtB,MAAM,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;QAEvC,MAAM,IAAI,GAAG,KAAK,CAAC;IACrB;AACF", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/types/index.ts"], "sourcesContent": ["export interface UserData {\n  name: string;\n  startupName: string;\n}\n\nexport interface ChatMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp?: number;\n}\n\nexport interface CallState {\n  isActive: boolean;\n  currentLevel: number;\n  isListening: boolean;\n  isAISpeaking: boolean;\n  transcript: TranscriptEntry[];\n  currentQuestion: string;\n  sessionId?: string;\n}\n\nexport interface TranscriptEntry {\n  speaker: 'user' | 'ai';\n  message: string;\n  timestamp: number;\n}\n\nexport interface EscalationLevel {\n  level: number;\n  title: string;\n  questions: string[];\n  description?: string;\n}\n\nexport interface PitchScore {\n  confidence: number;\n  clarity: number;\n  vision: number;\n  overall: number;\n}\n\nexport interface PitchResults {\n  transcript: string;\n  summary: string;\n  scores: PitchScore;\n  feedback: string[];\n  strengths: string[];\n  improvements: string[];\n  duration?: number;\n  sessionId?: string;\n}\n\nexport interface VoiceSettings {\n  stability: number;\n  similarity_boost: number;\n  style: number;\n  use_speaker_boost: boolean;\n}\n\nexport interface ElevenLabsRequest {\n  text: string;\n  model_id: string;\n  voice_settings: VoiceSettings;\n}\n\nexport interface SessionData {\n  userName: string;\n  startupName: string;\n  transcript: TranscriptEntry[];\n  duration: number;\n  startTime: number;\n  endTime: number;\n}\n\nexport interface APIResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface ChatResponse {\n  message: string;\n  nextLevel: number;\n  shouldEscalate: boolean;\n}\n\nexport interface VoiceResponse {\n  success: boolean;\n  audioUrl?: string;\n  duration?: number;\n  message?: string;\n}\n\n// Constants\nexport const ESCALATION_LEVELS: EscalationLevel[] = [\n  {\n    level: 1,\n    title: \"Icebreakers\",\n    description: \"Getting to know your startup and the problem you're solving\",\n    questions: [\n      \"Tell me what you're building.\",\n      \"What's the problem you solve?\",\n      \"How did you come up with this idea?\"\n    ]\n  },\n  {\n    level: 2,\n    title: \"Market\",\n    description: \"Understanding your market and target audience\",\n    questions: [\n      \"How big is this market really?\",\n      \"Who's your target user?\",\n      \"How do you know people want this?\",\n      \"What's your go-to-market strategy?\"\n    ]\n  },\n  {\n    level: 3,\n    title: \"Product\",\n    description: \"Diving into your product and technology\",\n    questions: [\n      \"Why now?\",\n      \"What makes your tech defensible?\",\n      \"What's your unfair advantage?\",\n      \"How does your product work?\"\n    ]\n  },\n  {\n    level: 4,\n    title: \"Business\",\n    description: \"Exploring your business model and strategy\",\n    questions: [\n      \"What's your moat?\",\n      \"What if a major competitor enters the space?\",\n      \"How do you plan to make money?\",\n      \"What are your unit economics?\"\n    ]\n  },\n  {\n    level: 5,\n    title: \"Gut Punch\",\n    description: \"Challenging questions to test your resilience\",\n    questions: [\n      \"This sounds like a feature, not a company. Convince me otherwise.\",\n      \"Why won't this be commoditized in 2 years?\",\n      \"What if Google builds this tomorrow?\",\n      \"Why should I invest in you versus the 100 other startups I see?\"\n    ]\n  }\n];\n\nexport const VOICE_SETTINGS: VoiceSettings = {\n  stability: 0.5,\n  similarity_boost: 0.5,\n  style: 0.0,\n  use_speaker_boost: true\n};\n\nexport const SCORE_THRESHOLDS = {\n  EXCELLENT: 90,\n  GOOD: 75,\n  AVERAGE: 60,\n  NEEDS_IMPROVEMENT: 40\n} as const;\n\nexport type ScoreLevel = keyof typeof SCORE_THRESHOLDS;\n"], "names": [], "mappings": ";;;;;AA+FO,MAAM,oBAAuC;IAClD;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,iBAAgC;IAC3C,WAAW;IACX,kBAAkB;IAClB,OAAO;IACP,mBAAmB;AACrB;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,SAAS;IACT,mBAAmB;AACrB", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport { PitchScore, SCORE_THRESHOLDS, ScoreLevel } from \"@/types\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDuration(seconds: number): string {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function getScoreLevel(score: number): ScoreLevel {\n  if (score >= SCORE_THRESHOLDS.EXCELLENT) return 'EXCELLENT';\n  if (score >= SCORE_THRESHOLDS.GOOD) return 'GOOD';\n  if (score >= SCORE_THRESHOLDS.AVERAGE) return 'AVERAGE';\n  return 'NEEDS_IMPROVEMENT';\n}\n\nexport function getScoreColor(score: number): string {\n  const level = getScoreLevel(score);\n  switch (level) {\n    case 'EXCELLENT':\n      return 'text-green-500';\n    case 'GOOD':\n      return 'text-blue-500';\n    case 'AVERAGE':\n      return 'text-yellow-500';\n    case 'NEEDS_IMPROVEMENT':\n      return 'text-red-500';\n    default:\n      return 'text-gray-500';\n  }\n}\n\nexport function getScoreGradient(score: number): string {\n  const level = getScoreLevel(score);\n  switch (level) {\n    case 'EXCELLENT':\n      return 'from-green-500 to-emerald-600';\n    case 'GOOD':\n      return 'from-blue-500 to-cyan-600';\n    case 'AVERAGE':\n      return 'from-yellow-500 to-orange-600';\n    case 'NEEDS_IMPROVEMENT':\n      return 'from-red-500 to-pink-600';\n    default:\n      return 'from-gray-500 to-gray-600';\n  }\n}\n\nexport function calculateOverallScore(scores: Omit<PitchScore, 'overall'>): number {\n  const { confidence, clarity, vision } = scores;\n  return Math.round((confidence + clarity + vision) / 3);\n}\n\nexport function generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\nexport function formatTimestamp(timestamp: number): string {\n  return new Date(timestamp).toLocaleTimeString();\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function validateUserData(data: any): data is { name: string; startupName: string } {\n  return (\n    data &&\n    typeof data === 'object' &&\n    typeof data.name === 'string' &&\n    typeof data.startupName === 'string' &&\n    data.name.trim().length > 0 &&\n    data.startupName.trim().length > 0\n  );\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.trim().replace(/[<>]/g, '');\n}\n\nexport function generateShareText(\n  userName: string,\n  startupName: string,\n  scores: PitchScore\n): string {\n  return `Just practiced my VC pitch for ${startupName} with Pitchpal! 🚀\n\nOverall Score: ${scores.overall}/100\n• Confidence: ${scores.confidence}/100\n• Clarity: ${scores.clarity}/100\n• Vision: ${scores.vision}/100\n\nReady to pitch to real investors! 💪\n\nTry Pitchpal: ${window.location.origin}`;\n}\n\nexport function copyToClipboard(text: string): Promise<boolean> {\n  if (navigator.clipboard && window.isSecureContext) {\n    return navigator.clipboard.writeText(text).then(() => true).catch(() => false);\n  } else {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    textArea.style.position = 'absolute';\n    textArea.style.left = '-999999px';\n    document.body.prepend(textArea);\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      return Promise.resolve(true);\n    } catch (error) {\n      return Promise.resolve(false);\n    } finally {\n      textArea.remove();\n    }\n  }\n}\n\nexport function isWebRTCSupported(): boolean {\n  return !!(\n    navigator.mediaDevices &&\n    navigator.mediaDevices.getUserMedia &&\n    window.RTCPeerConnection\n  );\n}\n\nexport function isSpeechRecognitionSupported(): boolean {\n  return !!(\n    window.SpeechRecognition ||\n    (window as any).webkitSpeechRecognition\n  );\n}\n\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,cAAc,KAAa;IACzC,IAAI,SAAS,qHAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE,OAAO;IAChD,IAAI,SAAS,qHAAA,CAAA,mBAAgB,CAAC,IAAI,EAAE,OAAO;IAC3C,IAAI,SAAS,qHAAA,CAAA,mBAAgB,CAAC,OAAO,EAAE,OAAO;IAC9C,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,QAAQ,cAAc;IAC5B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,KAAa;IAC5C,MAAM,QAAQ,cAAc;IAC5B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,sBAAsB,MAAmC;IACvE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IACxC,OAAO,KAAK,KAAK,CAAC,CAAC,aAAa,UAAU,MAAM,IAAI;AACtD;AAEO,SAAS;IACd,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,OAAO,IAAI,KAAK,WAAW,kBAAkB;AAC/C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,iBAAiB,IAAS;IACxC,OACE,QACA,OAAO,SAAS,YAChB,OAAO,KAAK,IAAI,KAAK,YACrB,OAAO,KAAK,WAAW,KAAK,YAC5B,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,KAC1B,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG;AAErC;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,SAAS,kBACd,QAAgB,EAChB,WAAmB,EACnB,MAAkB;IAElB,OAAO,CAAC,+BAA+B,EAAE,YAAY;;eAExC,EAAE,OAAO,OAAO,CAAC;cAClB,EAAE,OAAO,UAAU,CAAC;WACvB,EAAE,OAAO,OAAO,CAAC;UAClB,EAAE,OAAO,MAAM,CAAC;;;;cAIZ,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE;AACxC;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;QACjD,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAM,MAAM,KAAK,CAAC,IAAM;IAC1E,OAAO;QACL,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;QAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;QACtB,SAAS,IAAI,CAAC,OAAO,CAAC;QACtB,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,OAAO,QAAQ,OAAO,CAAC;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,QAAQ,OAAO,CAAC;QACzB,SAAU;YACR,SAAS,MAAM;QACjB;IACF;AACF;AAEO,SAAS;IACd,OAAO,CAAC,CAAC,CACP,UAAU,YAAY,IACtB,UAAU,YAAY,CAAC,YAAY,IACnC,OAAO,iBAAiB,AAC1B;AACF;AAEO,SAAS;IACd,OAAO,CAAC,CAAC,CACP,OAAO,iBAAiB,IACxB,AAAC,OAAe,uBAAuB,AACzC;AACF;AAEO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/call/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport VideoCall from '@/components/VideoCall';\nimport <PERSON>Handler, { synthesizeWithElevenLabs } from '@/components/AudioHandler';\nimport { UserData, CallState, TranscriptEntry, ESCALATION_LEVELS } from '@/types';\nimport { generateSessionId } from '@/lib/utils';\n\nexport default function CallPage() {\n  const [userData, setUserData] = useState<UserData | null>(null);\n  const [callState, setCallState] = useState<CallState>({\n    isActive: false,\n    currentLevel: 1,\n    isListening: false,\n    isAISpeaking: false,\n    transcript: [],\n    currentQuestion: \"\",\n    sessionId: generateSessionId()\n  });\n  const [error, setError] = useState<string>('');\n  const router = useRouter();\n\n  useEffect(() => {\n    // Get user data from sessionStorage\n    const storedData = sessionStorage.getItem('pitchpal_user');\n    if (!storedData) {\n      router.push('/');\n      return;\n    }\n    setUserData(JSON.parse(storedData));\n  }, [router]);\n\n  const startCall = async () => {\n    if (!userData) return;\n\n    setCallState(prev => ({ ...prev, isActive: true }));\n\n    // Start with first question\n    const firstQuestion = `Hello ${userData.name}, I'm excited to hear about ${userData.startupName}. ${ESCALATION_LEVELS[0].questions[0]}`;\n\n    setCallState(prev => ({\n      ...prev,\n      currentQuestion: firstQuestion,\n      isAISpeaking: true\n    }));\n\n    // Add AI message to transcript\n    const aiMessage: TranscriptEntry = {\n      speaker: 'ai',\n      message: firstQuestion,\n      timestamp: Date.now()\n    };\n\n    setCallState(prev => ({\n      ...prev,\n      transcript: [...prev.transcript, aiMessage]\n    }));\n\n    await speakQuestion(firstQuestion);\n  };\n\n  const speakQuestion = async (question: string) => {\n    try {\n      // Try ElevenLabs first, fallback to browser synthesis\n      await synthesizeWithElevenLabs(question);\n    } catch (error) {\n      console.error('Voice synthesis error:', error);\n      // Fallback to browser speech synthesis\n      if ('speechSynthesis' in window) {\n        const utterance = new SpeechSynthesisUtterance(question);\n        window.speechSynthesis.speak(utterance);\n      }\n    }\n\n    // After speaking, start listening\n    setTimeout(() => {\n      setCallState(prev => ({\n        ...prev,\n        isAISpeaking: false,\n        isListening: true\n      }));\n    }, 3000);\n  };\n\n  const handleSpeechResult = async (transcript: string) => {\n    if (!userData) return;\n\n    // Add user message to transcript\n    const userMessage: TranscriptEntry = {\n      speaker: 'user',\n      message: transcript,\n      timestamp: Date.now()\n    };\n\n    setCallState(prev => ({\n      ...prev,\n      transcript: [...prev.transcript, userMessage],\n      isListening: false\n    }));\n\n    // Get AI response\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          messages: [\n            { role: 'user', content: transcript }\n          ],\n          currentLevel: callState.currentLevel,\n          userName: userData.name,\n          startupName: userData.startupName\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.message) {\n        setCallState(prev => ({\n          ...prev,\n          currentQuestion: data.message,\n          isAISpeaking: true,\n          currentLevel: data.shouldEscalate ? data.nextLevel : prev.currentLevel\n        }));\n\n        // Add AI response to transcript\n        const aiResponse: TranscriptEntry = {\n          speaker: 'ai',\n          message: data.message,\n          timestamp: Date.now()\n        };\n\n        setCallState(prev => ({\n          ...prev,\n          transcript: [...prev.transcript, aiResponse]\n        }));\n\n        await speakQuestion(data.message);\n      }\n    } catch (error) {\n      console.error('Chat API error:', error);\n      setError('Failed to get AI response');\n    }\n  };\n\n  const handleSpeechEnd = () => {\n    // Speech recognition ended\n  };\n\n  const handleAudioError = (error: string) => {\n    setError(error);\n  };\n\n  const endCall = async () => {\n    if (!userData) return;\n\n    // Save session data\n    const sessionData = {\n      userName: userData.name,\n      startupName: userData.startupName,\n      transcript: callState.transcript,\n      duration: Math.floor((Date.now() - (callState.transcript[0]?.timestamp || Date.now())) / 1000),\n      startTime: callState.transcript[0]?.timestamp || Date.now(),\n      endTime: Date.now()\n    };\n\n    try {\n      const response = await fetch('/api/session', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(sessionData)\n      });\n\n      const results = await response.json();\n\n      // Store results for the results page\n      sessionStorage.setItem('pitchpal_results', JSON.stringify(results));\n\n    } catch (error) {\n      console.error('Session save error:', error);\n    }\n\n    setCallState(prev => ({ ...prev, isActive: false }));\n    router.push('/results');\n  };\n\n  if (!userData) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-white text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black flex flex-col\">\n      {/* Header with call controls */}\n      <div className=\"flex justify-between items-center p-4 bg-gray-900/50 backdrop-blur-sm z-10\">\n        <div className=\"text-white\">\n          <h2 className=\"text-lg font-semibold\">{userData.name}</h2>\n          <p className=\"text-gray-400 text-sm\">{userData.startupName}</p>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {callState.isActive && (\n            <div className=\"text-white text-sm\">\n              Level {callState.currentLevel}: {ESCALATION_LEVELS[callState.currentLevel - 1]?.title}\n            </div>\n          )}\n\n          <button\n            onClick={endCall}\n            className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          >\n            End Call\n          </button>\n        </div>\n      </div>\n\n      {/* Error display */}\n      {error && (\n        <div className=\"bg-red-600 text-white p-3 text-center\">\n          {error}\n          <button\n            onClick={() => setError('')}\n            className=\"ml-4 underline\"\n          >\n            Dismiss\n          </button>\n        </div>\n      )}\n\n      {/* Video Call Component */}\n      <VideoCall\n        isActive={callState.isActive}\n        isListening={callState.isListening}\n        isAISpeaking={callState.isAISpeaking}\n        currentQuestion={callState.currentQuestion}\n        onStartCall={startCall}\n        onEndCall={endCall}\n      />\n\n      {/* Audio Handler Component */}\n      <AudioHandler\n        isListening={callState.isListening}\n        onSpeechResult={handleSpeechResult}\n        onSpeechEnd={handleSpeechEnd}\n        onError={handleAudioError}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,UAAU;QACV,cAAc;QACd,aAAa;QACb,cAAc;QACd,YAAY,EAAE;QACd,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD;IAC7B;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,aAAa,eAAe,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,CAAC;YACZ;QACF;QACA,YAAY,KAAK,KAAK,CAAC;IACzB,GAAG;QAAC;KAAO;IAEX,MAAM,YAAY;QAChB,IAAI,CAAC,UAAU;QAEf,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAK,CAAC;QAEjD,4BAA4B;QAC5B,MAAM,gBAAgB,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,4BAA4B,EAAE,SAAS,WAAW,CAAC,EAAE,EAAE,qHAAA,CAAA,oBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;QAEvI,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,iBAAiB;gBACjB,cAAc;YAChB,CAAC;QAED,+BAA+B;QAC/B,MAAM,YAA6B;YACjC,SAAS;YACT,SAAS;YACT,WAAW,KAAK,GAAG;QACrB;QAEA,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;iBAAU;YAC7C,CAAC;QAED,MAAM,cAAc;IACtB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,sDAAsD;YACtD,MAAM,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uCAAuC;YACvC,IAAI,qBAAqB,QAAQ;gBAC/B,MAAM,YAAY,IAAI,yBAAyB;gBAC/C,OAAO,eAAe,CAAC,KAAK,CAAC;YAC/B;QACF;QAEA,kCAAkC;QAClC,WAAW;YACT,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,cAAc;oBACd,aAAa;gBACf,CAAC;QACH,GAAG;IACL;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,UAAU;QAEf,iCAAiC;QACjC,MAAM,cAA+B;YACnC,SAAS;YACT,SAAS;YACT,WAAW,KAAK,GAAG;QACrB;QAEA,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;iBAAY;gBAC7C,aAAa;YACf,CAAC;QAED,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;wBACR;4BAAE,MAAM;4BAAQ,SAAS;wBAAW;qBACrC;oBACD,cAAc,UAAU,YAAY;oBACpC,UAAU,SAAS,IAAI;oBACvB,aAAa,SAAS,WAAW;gBACnC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,iBAAiB,KAAK,OAAO;wBAC7B,cAAc;wBACd,cAAc,KAAK,cAAc,GAAG,KAAK,SAAS,GAAG,KAAK,YAAY;oBACxE,CAAC;gBAED,gCAAgC;gBAChC,MAAM,aAA8B;oBAClC,SAAS;oBACT,SAAS,KAAK,OAAO;oBACrB,WAAW,KAAK,GAAG;gBACrB;gBAEA,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,YAAY;+BAAI,KAAK,UAAU;4BAAE;yBAAW;oBAC9C,CAAC;gBAED,MAAM,cAAc,KAAK,OAAO;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB;IACtB,2BAA2B;IAC7B;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;IACX;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,UAAU;QAEf,oBAAoB;QACpB,MAAM,cAAc;YAClB,UAAU,SAAS,IAAI;YACvB,aAAa,SAAS,WAAW;YACjC,YAAY,UAAU,UAAU;YAChC,UAAU,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,UAAU,CAAC,EAAE,EAAE,aAAa,KAAK,GAAG,EAAE,CAAC,IAAI;YACzF,WAAW,UAAU,UAAU,CAAC,EAAE,EAAE,aAAa,KAAK,GAAG;YACzD,SAAS,KAAK,GAAG;QACnB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YAEnC,qCAAqC;YACrC,eAAe,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QAE5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;QAEA,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAM,CAAC;QAClD,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;0BAAqB;;;;;;;;;;;IAG1C;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAyB,SAAS,IAAI;;;;;;0CACpD,6WAAC;gCAAE,WAAU;0CAAyB,SAAS,WAAW;;;;;;;;;;;;kCAG5D,6WAAC;wBAAI,WAAU;;4BACZ,UAAU,QAAQ,kBACjB,6WAAC;gCAAI,WAAU;;oCAAqB;oCAC3B,UAAU,YAAY;oCAAC;oCAAG,qHAAA,CAAA,oBAAiB,CAAC,UAAU,YAAY,GAAG,EAAE,EAAE;;;;;;;0CAIpF,6WAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOJ,uBACC,6WAAC;gBAAI,WAAU;;oBACZ;kCACD,6WAAC;wBACC,SAAS,IAAM,SAAS;wBACxB,WAAU;kCACX;;;;;;;;;;;;0BAOL,6WAAC,+HAAA,CAAA,UAAS;gBACR,UAAU,UAAU,QAAQ;gBAC5B,aAAa,UAAU,WAAW;gBAClC,cAAc,UAAU,YAAY;gBACpC,iBAAiB,UAAU,eAAe;gBAC1C,aAAa;gBACb,WAAW;;;;;;0BAIb,6WAAC,kIAAA,CAAA,UAAY;gBACX,aAAa,UAAU,WAAW;gBAClC,gBAAgB;gBAChB,aAAa;gBACb,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}